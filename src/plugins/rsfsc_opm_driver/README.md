# Thorlabs 光功率计驱动插件

## 概述

本插件为工装平台测试软件提供 Thorlabs 光功率计的驱动支持，允许用户在 BehaviorTree 流程中集成光功率测量功能。

## 功能特性

- **设备连接管理**：支持通过串口或 USB 连接 Thorlabs 光功率计
- **功率测量**：实时读取光功率值（dBm 单位）
- **设备配置**：支持设置测量波长、平均次数等参数
- **设备信息获取**：获取设备型号、序列号、固件版本等信息
- **异步操作**：所有操作均为异步执行，不阻塞 UI 线程

## 支持的节点类型

### 1. OPMConnect - 连接光功率计

**功能**：建立与光功率计的连接
**输入参数**：

- `device_id`：设备 ID 或串口名称（如 COM3, /dev/ttyUSB0）
- `timeout_ms`：连接超时时间（毫秒，默认 5000）

**输出参数**：

- `opm_handle`：设备句柄，用于后续操作

### 2. OPMDisconnect - 断开光功率计

**功能**：断开与光功率计的连接
**输入参数**：

- `opm_handle`：设备句柄

### 3. OPMReadPower - 读取光功率

**功能**：读取当前光功率值
**输入参数**：

- `opm_handle`：设备句柄

**输出参数**：

- `power_dbm`：光功率值（dBm）

### 4. OPMConfigure - 配置光功率计

**功能**：配置设备参数
**输入参数**：

- `opm_handle`：设备句柄
- `wavelength_nm`：测量波长（纳米，默认 1550.0）
- `average_count`：测量平均次数（默认 10）

### 5. OPMGetDeviceInfo - 获取设备信息

**功能**：获取设备详细信息
**输入参数**：

- `opm_handle`：设备句柄

**输出参数**：

- `device_info`：设备信息字符串

## 使用示例

### 基本测量流程

```xml
<Sequence>
  <!-- 连接设备 -->
  <OPMConnect device_id="COM3" timeout_ms="5000" opm_handle="{opm_handle}" />

  <!-- 配置设备 -->
  <OPMConfigure
      opm_handle="{opm_handle}"
      wavelength_nm="1550.0"
      average_count="10" />

  <!-- 读取功率 -->
  <OPMReadPower opm_handle="{opm_handle}" power_dbm="{power_value}" />

  <!-- 添加测量数据 -->
  <AddNumericMeasure
      label="光功率"
      lower_limit="-50.0"
      upper_limit="-10.0"
      unit="dBm"
      variable_name="power_value"
      data_type="0" />

  <!-- 断开连接 -->
  <OPMDisconnect omp_handle="{opm_handle}" />
</Sequence>
```

### 多次测量流程

```xml
<Sequence>
  <OPMConnect device_id="COM3" opm_handle="{opm_handle}" />
  <OPMConfigure opm_handle="{opm_handle}" wavelength_nm="1550.0" />

  <!-- 重复测量3次 -->
  <Repeat num_cycles="3">
    <Sequence>
      <OPMReadPower opm_handle="{opm_handle}" power_dbm="{power_value}" />
      <Sleep 延时="1000" />
    </Sequence>
  </Repeat>

  <OPMDisconnect opm_handle="{opm_handle}" />
</Sequence>
```

## 技术实现

### 架构设计

- **ThorlabsOPMHandle**：设备句柄类，管理设备连接和操作
- **异步节点**：使用 BT::CoroActionNode 和 BT::StatefulActionNode 实现异步操作
- **错误处理**：完善的错误检查和日志记录

### 当前实现状态

- ✅ 插件框架完成
- ✅ 节点注册和 UI 集成
- ✅ 模拟设备驱动（用于测试）
- ⚠️ 实际硬件驱动待实现

### 扩展实际硬件驱动

要连接真实的 Thorlabs 设备，需要在`ThorlabsOPMHandle`类中实现：

1 **VISA 驱动集成**：

```cpp
#include <visa.h>
// 在connect()方法中使用viOpen()建立连接
```

2 **串口通信**：

```cpp
#include <QSerialPort>
// 使用Qt串口类进行通信
```

3 **SCPI 命令**：

```cpp
// 实现标准SCPI命令发送和响应解析
bool sendCommand(const std::string& command);
std::string readResponse();
```

## 编译和安装

插件会自动随主项目编译，生成的库文件位于：

```
build/plugins/librsfsc_opm_driver.so
```

## 故障排除

### 常见问题

1 **设备连接失败**

   - 检查设备 ID 是否正确
   - 确认设备驱动已安装
   - 验证串口权限设置

2 **读取数据异常**

   - 检查设备是否正确配置
   - 确认波长设置是否匹配
   - 验证设备连接状态

3 **插件加载失败**

   - 确认插件文件存在
   - 检查依赖库是否完整
   - 查看应用程序日志

### 调试信息

插件会输出详细的调试日志，可通过以下方式查看：

```bash
# 查看实时日志
tail -f logs/fixture_test.log | grep OPM

# 搜索错误信息
grep "OPM.*ERROR" logs/fixture_test.log
```

## 版本历史

- **v0.1.0**：初始版本，包含基本功能和模拟驱动

## 许可证

版权所有 © 2025 RoboSense。保留所有权利。
