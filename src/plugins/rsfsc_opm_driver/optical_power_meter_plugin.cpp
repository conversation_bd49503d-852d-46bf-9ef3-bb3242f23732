/******************************************************************************
 * Copyright 2025 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "optical_power_meter_plugin.h"
#include "bt_node_opm_driver.h"
#include "node_models/bt_node_model.h"
#include <utility>

namespace robosense::lidar
{

std::string OpticalPowerMeterPlugin::name() const { return "Thorlabs光功率计"; }
std::string OpticalPowerMeterPlugin::version() const { return "1.0.0"; }

bool OpticalPowerMeterPlugin::registerNodes()
{
  // 注册OPM连接节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "OPMConnect";
    model.display_name    = "连接光功率计";
    model.setInstanceName("OPMConnect");
    PortModels ports;

    // 设备句柄输出端口
    PortModel opm_handle_port;
    opm_handle_port.type_name     = "opm_handle";
    opm_handle_port.direction     = BT::PortDirection::OUTPUT;
    opm_handle_port.description   = "光功率计设备句柄";
    opm_handle_port.default_value = "{opm_handle}";
    opm_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(opm_handle_port) });

    // 设备ID输入端口
    PortModel device_id_port;
    device_id_port.type_name     = "opm_sn";
    device_id_port.direction     = BT::PortDirection::INPUT;
    device_id_port.description   = "光功率计编号名称(如*********)";
    device_id_port.default_value = "*********";
    device_id_port.data_type     = PortDataType::String;
    ports.insert({ "光功率计编号", std::move(device_id_port) });

    // 测量的功率范围
    PortModel power_range_port;
    power_range_port.type_name     = "power_range";
    power_range_port.direction     = BT::PortDirection::INPUT;
    power_range_port.description   = "测量的功率范围(W)";
    power_range_port.default_value = "0.02";
    power_range_port.data_type     = PortDataType::Double;
    power_range_port.min_value     = 0.0;
    power_range_port.max_value     = 100.0;
    ports.insert({ "测量的功率范围", std::move(power_range_port) });

    // 测量波长值
    PortModel wave_length_port;
    wave_length_port.type_name     = "wave_length";
    wave_length_port.direction     = BT::PortDirection::INPUT;
    wave_length_port.description   = "测量波长值(nm)";
    wave_length_port.default_value = "905.0";
    wave_length_port.data_type     = PortDataType::Double;
    wave_length_port.min_value     = 400.0;
    wave_length_port.max_value     = 1700.0;
    ports.insert({ "测量波长值", std::move(wave_length_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<OPMConnect, BtNodeModel>("OPMConnect", "Action", model);
  }

  // 注册OPM断开连接节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "OPMDisconnect";
    model.display_name    = "断开光功率计";
    model.setInstanceName("OPMDisconnect");
    PortModels ports;

    // 设备句柄输入端口
    PortModel opm_handle_port;
    opm_handle_port.type_name     = "opm_handle";
    opm_handle_port.direction     = BT::PortDirection::INPUT;
    opm_handle_port.description   = "光功率计设备句柄";
    opm_handle_port.default_value = "{opm_handle}";
    opm_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(opm_handle_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<OPMDisconnect, BtNodeModel>("OPMDisconnect", "Action", model);
  }

  // 注册OPM功率读取节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "OPMReadPower";
    model.display_name    = "读取光功率";
    model.setInstanceName("OPMReadPower");
    PortModels ports;

    // 设备句柄输入端口
    PortModel opm_handle_port;
    opm_handle_port.type_name     = "opm_handle";
    opm_handle_port.direction     = BT::PortDirection::INPUT;
    opm_handle_port.description   = "光功率计设备句柄";
    opm_handle_port.default_value = "{opm_handle}";
    opm_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(opm_handle_port) });

    // 功率值输出端口
    PortModel power_port;
    power_port.type_name     = "power_mw";
    power_port.direction     = BT::PortDirection::OUTPUT;
    power_port.description   = "读取的光功率值(mW)";
    power_port.default_value = "{power_mw}";
    power_port.data_type     = PortDataType::Double;
    ports.insert({ "功率值", std::move(power_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<OPMReadPower, BtNodeModel>("OPMReadPower", "Action", model);
  }

  // 注册OPM配置节点
  {
    NodeModel model;
    model.type            = BT::NodeType::ACTION;
    model.registration_id = "OPMConfigure";
    model.display_name    = "配置光功率计";
    model.setInstanceName("OPMConfigure");
    PortModels ports;

    // 设备句柄输入端口
    PortModel opm_handle_port;
    opm_handle_port.type_name     = "opm_handle";
    opm_handle_port.direction     = BT::PortDirection::INPUT;
    opm_handle_port.description   = "光功率计设备句柄";
    opm_handle_port.default_value = "{opm_handle}";
    opm_handle_port.data_type     = PortDataType::String;
    ports.insert({ "设备句柄", std::move(opm_handle_port) });

    // 平均次数输入端口
    PortModel average_port;
    average_port.type_name     = "average_count";
    average_port.direction     = BT::PortDirection::INPUT;
    average_port.description   = "测量平均次数";
    average_port.default_value = "100";
    average_port.data_type     = PortDataType::Integer;
    average_port.min_value     = 1;
    average_port.max_value     = 1000;
    ports.insert({ "平均次数", std::move(average_port) });

    // 测量的功率范围
    PortModel power_range_port;
    power_range_port.type_name     = "power_range";
    power_range_port.direction     = BT::PortDirection::INPUT;
    power_range_port.description   = "测量的功率范围(W)";
    power_range_port.default_value = "0.02";
    power_range_port.data_type     = PortDataType::Double;
    power_range_port.min_value     = 0.0;
    power_range_port.max_value     = 100.0;
    ports.insert({ "测量的功率范围", std::move(power_range_port) });

    model.ports = std::move(ports);
    registerNodeTypeModel<OPMConfigure, BtNodeModel>("OPMConfigure", "Action", model);
  }

  return true;
}

}  // namespace robosense::lidar