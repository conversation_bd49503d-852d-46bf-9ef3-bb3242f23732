# 工装平台测试软件操作手册

## 目录

1. [界面介绍](#1-界面介绍)
2. [基本操作](#2-基本操作)
3. [流程编辑](#3-流程编辑)
4. [节点类型](#4-节点类型)
5. [参数配置](#5-参数配置)
6. [测试执行](#6-测试执行)
7. [文件管理](#7-文件管理)
8. [插件管理](#8-插件管理)
9. [常见问题](#9-常见问题)
10. [最佳实践](#10-最佳实践)
11. [高级功能](#11-高级功能)

## 1. 界面介绍

### 1.1 主界面布局

软件主界面采用停靠窗口设计，包含以下区域：

```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏 [文件] [工具] [帮助]                                │
├─────────────────────────────────────────────────────────┤
│ 工具栏 [新建] [打开] [保存] [节点对齐]                      │
├──────────┬─────────────────────────────┬────────────────┤
│          │                             │                │
│ 节点面板 │        流程编辑区           │   属性面板         │
│          │                             │                │
│          │                             │                │
├──────────┼─────────────────────────────┼────────────────┤
│          │                             │                │
│ 控制面板 │                             │   监控面板     │
│          │                             │                │
├──────────┴─────────────────────────────┴────────────────┤
│                    消息日志区                           │
└─────────────────────────────────────────────────────────┘
```

### 1.2 各区域功能

#### 节点面板（左上）

- 显示所有可用的节点类型
- 按类别分组：根节点、控制节点、动作节点、条件节点、装饰节点
- 支持拖拽节点到流程编辑区

#### 流程编辑区（中央）

- 主要的流程设计区域
- 支持节点拖拽、连接、移动等操作
- 实时显示节点执行状态

#### 属性面板（右上）

- 显示选中节点的属性配置
- 支持参数修改和保存
- 自动同步到流程数据

#### 控制面板（左下）

- 雷达信息配置
- 测试控制按钮
- 系统参数设置

#### 监控面板（右下）

- 测试状态监控
- 实时数据显示
- 错误信息提示

#### 消息日志区（底部）

- 系统运行日志
- 错误和警告信息
- 调试信息输出

---

## 2. 基本操作

### 2.1 创建新流程

1. 点击菜单栏 **文件** → **新建** 或工具栏 **新建** 按钮
2. 系统会清空当前流程编辑区
3. 从节点面板拖拽"开始"节点到编辑区作为根节点

### 2.2 添加节点

1. 从左侧节点面板选择需要的节点类型
2. 按住鼠标左键拖拽到流程编辑区
3. 松开鼠标完成节点添加

### 2.3 连接节点

1. 将鼠标悬停在源节点的输出端口上
2. 按住鼠标左键拖拽到目标节点的输入端口
3. 松开鼠标完成连接

### 2.4 编辑节点属性

1. 双击节点打开属性编辑对话框
2. 修改节点参数
3. 点击确定保存修改

### 2.5 删除节点

1. 右键点击节点
2. 选择"删除节点"
3. 确认删除操作

---

## 3. 流程编辑

### 3.1 编辑锁定功能

为防止意外修改，软件提供编辑锁定功能：

#### 锁定操作

- **菜单方式**：工具 → 🔓 解锁编辑（点击切换为锁定）
- **工具栏方式**：点击工具栏的锁定按钮
- **快捷键**：`Ctrl+L`

#### 锁定状态下的行为

- ✅ **允许**：拖动整个视野浏览流程
- ✅ **允许**：缩放视图查看细节
- ❌ **禁止**：移动单个节点
- ❌ **禁止**：编辑节点属性
- ❌ **禁止**：添加或删除节点
- ❌ **禁止**：修改节点连接

#### 视野操作

锁定状态下可以通过以下方式浏览流程：

- 在空白区域拖拽移动视野
- 使用鼠标滚轮缩放
- 中键拖拽（如果支持）

### 3.2 节点对齐

点击工具栏的"节点对齐"按钮，系统会自动整理节点布局，使流程图更加美观。

### 3.3 流程验证

系统会自动验证流程的正确性：

- 检查节点连接的合法性
- 验证必需参数是否配置
- 提示潜在的逻辑错误

---

## 4. 节点类型

### 4.1 根节点

- **开始**：流程的起始节点，每个流程必须有且仅有一个

### 4.2 控制节点

- **Sequence**：顺序执行子节点，全部成功才成功
- **Fallback**：依次尝试子节点，有一个成功就成功
- **Parallel**：并行执行子节点
- **IfThenElse**：条件分支控制
- **WhileDoElse**：循环控制

### 4.3 动作节点

- **Sleep**：延时等待
- **SetBlackboard**：设置黑板变量
- **UnsetBlackboard**：清除黑板变量
- **AddNumericMeasure**：添加数值测量

### 4.4 条件节点

- **ScriptCondition**：脚本条件判断

### 4.5 装饰节点

- **ForceSuccess**：强制返回成功
- **Inverter**：反转子节点结果
- **RetryUntilSuccessful**：重试直到成功
- **KeepRunningUntilFailure**：持续运行直到失败
- **Repeat**：重复执行
- **Timeout**：超时控制

### 4.6 节点参数说明

#### SetBlackboard 节点

用于在黑板中设置变量值，供其他节点使用。

**参数配置：**

- `output_key`：变量名（不要加花括号）
- `value`：要设置的值

**正确用法示例：**

```xml
<SetBlackboard output_key="voltage_value" value="4.5" />
```

**错误用法：**

```xml
<SetBlackboard output_key="{voltage_value}" value="4.5" />
```

#### AddNumericMeasure 节点

用于添加数值测量项，支持从黑板变量获取测量值。

**参数配置：**

- `label`：测量项标签
- `lower_limit`：下限值
- `upper_limit`：上限值
- `unit`：单位
- `variable_name`：黑板变量名（可选）
- `data_type`：数据类型（0=浮点数，1=十六进制，2=整数）
- `data_value`：直接输入的测量值（当不使用变量时）

**使用变量示例：**

```xml
<AddNumericMeasure
    label="电压测量"
    lower_limit="3.0"
    upper_limit="5.0"
    unit="V"
    variable_name="voltage_value"
    data_type="0"
/>
```

#### Sleep 节点

用于在流程中添加延时等待。

**参数配置：**

- `延时`：等待时间（毫秒）

**示例：**

```xml
<Sleep 延时="1000" />  <!-- 等待1秒 -->
```

#### ScriptCondition 节点

用于执行脚本条件判断。

**参数配置：**

- `条件脚本`：要执行的条件脚本代码

---

## 5. 参数配置

### 5.1 雷达基本参数

在控制面板中可以配置雷达相关参数：

#### 项目信息

- **项目编号**：当前测试项目的编号
- **雷达安装位置**：雷达在系统中的安装位置

#### 网络配置

- **IP 地址**：雷达的网络 IP 地址
- **MSOP 端口**：主数据流端口号
- **DIFOP 端口**：设备信息端口号

#### 设备信息

- **雷达 SN**：设备序列号（支持扫码输入）

### 5.2 参数可见性控制

可以通过参数配置控制各个控件的显示：

- 项目编号是否可见
- 雷达安装位置是否可见
- IP 地址是否可见
- MSOP 端口号是否可见
- DIFOP 端口号是否可见
- 雷达 SN 是否可见

### 5.3 参数持久化

所有参数配置会自动保存到配置文件中，下次启动时自动加载。

---

## 6. 测试执行

### 6.1 开始测试

1. 确保流程设计完成
2. 配置必要的雷达参数
3. 点击"开始测试"按钮
4. 观察节点状态变化和日志输出

### 6.2 测试状态监控

测试过程中，节点会显示不同的状态：

- **灰色**：未执行
- **黄色**：正在执行
- **绿色**：执行成功
- **红色**：执行失败

### 6.3 测试结果

测试完成后，可以在日志区域查看详细的执行结果和测量数据。

---

## 7. 文件管理

### 7.1 文件格式

软件使用统一的`.rsfsc`文件格式，包含：

- BehaviorTree XML 数据
- QtNodes 节点布局信息
- 自定义参数配置

### 7.2 保存流程

- **保存**：`Ctrl+S` 或 文件 → 保存
- **另存为**：文件 → 另存为

### 7.3 加载流程

- **打开**：`Ctrl+O` 或 文件 → 打开
- 选择`.rsfsc`文件进行加载

### 7.4 自动保存

系统会自动保存当前配置，防止意外丢失。

---

## 8. 插件管理

### 8.1 加载插件

1. 点击菜单栏 **工具** → **加载插件**
2. 系统会扫描插件目录
3. 自动加载可用的插件节点

## 9. 常见问题

### 9.1 节点连接失败

**问题**：无法连接两个节点
**解决**：检查端口类型是否匹配，确保输出端口连接到输入端口

### 9.2 参数配置不生效

**问题**：修改参数后不生效
**解决**：确保点击了确定按钮保存参数，检查参数格式是否正确

### 9.3 测试执行失败

**问题**：点击开始测试后流程不执行
**解决**：检查流程是否有根节点，验证所有必需参数是否配置

### 9.4 插件加载失败

**问题**：自定义插件无法加载
**解决**：检查插件文件是否在正确目录，确认插件接口实现正确

### 9.5 文件保存失败

**问题**：无法保存流程文件
**解决**：检查文件路径权限，确保磁盘空间充足

### 9.6 变量使用错误

**问题**：`missing port [output_key]` 或 `Failed to get variable 'xxx' from blackboard`
**解决**：

- 确保 SetBlackboard 的`output_key`字段不使用花括号
- 确保在使用变量前先设置变量
- 检查变量名拼写和大小写

---

## 10. 最佳实践

### 10.1 流程设计原则

#### 模块化设计

- 将复杂流程分解为多个简单的子流程
- 使用 `Sequence` 和 `Fallback` 节点组织逻辑结构
- 避免过深的嵌套层次

#### 错误处理

- 为关键操作添加超时控制
- 使用 `Fallback` 节点提供备选方案
- 在适当位置添加重试机制

#### 参数管理

- 使用有意义的变量名
- 在流程开始时集中设置所有变量
- 在流程结束时清理不需要的变量

### 10.2 测试流程示例

#### 基本雷达测试流程

```xml
<Sequence>
  <!-- 初始化阶段 -->
  <SetBlackboard output_key="test_start_time" value="$(timestamp)" />
  <SetBlackboard output_key="expected_voltage" value="5.0" />

  <!-- 设备连接 -->
  <Sequence>
    <ConnectToRadar ip="{radar_ip}" port="{radar_port}" />
    <Sleep 延时="1000" />
    <CheckConnection />
  </Sequence>

  <!-- 测试执行 -->
  <Parallel>
    <Sequence>
      <ReadVoltage variable_name="actual_voltage" />
      <AddNumericMeasure
          label="电源电压"
          lower_limit="4.5"
          upper_limit="5.5"
          unit="V"
          variable_name="actual_voltage"
          data_type="0"
      />
    </Sequence>

    <Sequence>
      <ReadTemperature variable_name="temperature" />
      <AddNumericMeasure
          label="工作温度"
          lower_limit="-10"
          upper_limit="60"
          unit="°C"
          variable_name="temperature"
          data_type="0"
      />
    </Sequence>
  </Parallel>

  <!-- 清理阶段 -->
  <DisconnectFromRadar />
  <UnsetBlackboard key="actual_voltage" />
  <UnsetBlackboard key="temperature" />
</Sequence>
```

#### 条件分支测试流程

```xml
<Sequence>
  <SetBlackboard output_key="test_mode" value="production" />

  <IfThenElse>
    <ScriptCondition 条件脚本="test_mode == 'production'" />

    <!-- 生产模式测试 -->
    <Sequence>
      <ProductionTest />
      <GenerateReport />
    </Sequence>

    <!-- 调试模式测试 -->
    <Sequence>
      <DebugTest />
      <ShowDebugInfo />
    </Sequence>
  </IfThenElse>
</Sequence>
```

### 10.3 性能优化建议

#### 并行执行

- 对于独立的测试项目，使用 Parallel 节点并行执行
- 避免不必要的串行等待

#### 资源管理

- 及时释放不需要的连接和资源
- 使用 UnsetBlackboard 清理临时变量

#### 超时设置

- 为所有网络操作设置合理的超时时间
- 使用 Timeout 装饰节点包装可能阻塞的操作

---

## 11. 高级功能

### 11.1 自定义脚本条件

ScriptCondition 节点支持简单的条件表达式：

```javascript
// 数值比较
voltage > 4.5 && voltage < 5.5;

// 字符串比较
device_type ==
  "radar"(
    // 逻辑运算
    temperature > 0
  ) || test_mode == "debug";
```

### 11.2 变量作用域

黑板变量在整个测试流程中全局可见：

- 子节点可以访问父节点设置的变量
- 并行分支之间可以共享变量
- 变量在流程结束后自动清理

---

## 附录

### A. 快捷键列表

- `Ctrl+N`：新建流程
- `Ctrl+O`：打开文件
- `Ctrl+S`：保存文件
- `Ctrl+L`：切换编辑锁定
- `Ctrl+D`：复制节点

### B. 支持的数据类型

- 浮点数 (MEASURE_DATA_TYPE_FLOAT)
- 十六进制 (MEASURE_DATA_TYPE_HEX)
- 整数 (MEASURE_DATA_TYPE_INT)

### C. 技术支持

如有问题，请查看日志信息或联系技术支持团队。

#### 日志文件位置

- 应用程序日志：`logs/fixture_test.log`
- 错误日志：`logs/error.log`
- 调试日志：`logs/debug.log`

#### 常用调试命令

```bash
# 查看实时日志
tail -f logs/fixture_test.log

# 搜索错误信息
grep "ERROR" logs/fixture_test.log

# 查看最近的警告
grep "WARN" logs/fixture_test.log | tail -20
```

### D. 文件结构说明

```
fixture_test/
├── fixture_test           # 主程序可执行文件
├── plugins/              # 插件目录
│   ├── librsfsc_communication_plugin.so
│   └── librsfsc_log_setting_nodes.so
├── config/               # 配置文件目录
│   ├── auto_save.json   # 自动保存的配置
│   └── user_settings.json
├── logs/                 # 日志文件目录
├── doc/                  # 文档目录
│   ├── 操作手册.md
│   └── 使用说明.md
└── examples/             # 示例流程文件
    ├── basic_test.rsfsc
    └── advanced_test.rsfsc
```

### E. 版本历史

#### v1.0.0 (2024 年)

- 初始版本发布
- 基本的流程编辑功能
- 节点拖拽和连接
- 参数配置和保存

#### v1.1.0 (2024 年)

- 添加编辑锁定功能
- 支持视野拖动
- 改进雷达参数配置界面
- 优化节点布局算法

#### v1.2.0 (计划中)

- 子树功能支持
- 更多内置节点类型
- 性能优化
- 多文档界面(MDI)支持

---

## 总结

工装平台测试软件是一款功能强大的可视化测试流程设计工具，具有以下核心优势：

### 🎯 **易用性**

- 直观的拖拽式界面设计
- 丰富的节点类型和参数配置
- 实时的状态监控和反馈

### 🔧 **灵活性**

- 支持复杂的流程逻辑设计
- 可扩展的插件架构
- 灵活的参数配置管理

### 🛡️ **可靠性**

- 完善的错误处理机制
- 编辑锁定保护功能
- 自动保存和恢复机制

### 🚀 **高效性**

- 并行执行支持
- 智能的节点对齐
- 快捷键操作支持

通过本手册的学习，您应该能够：

- 熟练使用软件进行流程设计
- 配置和管理测试参数
- 执行和监控测试流程
- 解决常见的使用问题
- 开发自定义插件节点

如需更多帮助，请参考生产软件组内的帮助文档或联系技术支持团队。

---

_本手册版本：v1.2_
_最后更新：2024 年 12 月_
_适用软件版本：fixture_test v1.1.0+_
